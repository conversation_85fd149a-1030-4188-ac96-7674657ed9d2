/**
 * IndexedDB Storage Utility
 * Provides persistent storage for processed file data with automatic cleanup
 */

class IndexedDBStorage {
    constructor(options = {}) {
        this.dbName = options.dbName || 'DependencyVisualizerDB';
        this.dbVersion = options.dbVersion || 1;
        this.storeName = options.storeName || 'scanResults';
        this.maxStorageSize = options.maxStorageSize || 100 * 1024 * 1024; // 100MB
        this.maxAge = options.maxAge || 7 * 24 * 60 * 60 * 1000; // 7 days
        this.db = null;
        this.isInitialized = false;
    }

    /**
     * Initialize the IndexedDB database
     */
    async initialize() {
        if (this.isInitialized) return;

        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('IndexedDB initialization failed:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                this.isInitialized = true;
                console.log('IndexedDB initialized successfully');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object store if it doesn't exist
                if (!db.objectStoreNames.contains(this.storeName)) {
                    const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    store.createIndex('projectPath', 'projectPath', { unique: false });
                }
            };
        });
    }

    /**
     * Store scan results with automatic cleanup and data optimization
     */
    async storeScanResults(scanResults, projectPath = 'default') {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Optimize data before storing
        const optimizedData = this.optimizeScanResults(scanResults);

        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);

        const data = {
            id: `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            projectPath,
            timestamp: Date.now(),
            data: optimizedData,
            size: this.estimateSize(optimizedData),
            originalSize: this.estimateSize(scanResults),
            compressionRatio: this.estimateSize(optimizedData) / this.estimateSize(scanResults)
        };

        return new Promise((resolve, reject) => {
            const request = store.add(data);

            request.onsuccess = async () => {
                console.log(`Scan results stored to IndexedDB: ${data.id}`);
                console.log(`Storage optimization: ${Math.round((1 - data.compressionRatio) * 100)}% size reduction`);

                // Trigger cleanup after storing
                await this.cleanup();
                resolve(data.id);
            };

            request.onerror = () => {
                console.error('Failed to store scan results:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Optimize scan results for storage by removing redundant data
     */
    optimizeScanResults(scanResults) {
        const optimized = {
            files: {},
            graph: {
                nodes: [],
                edges: []
            },
            metadata: {
                ...scanResults.metadata,
                optimized: true,
                optimizedAt: Date.now()
            }
        };

        // Optimize file data - remove large content, keep only essential info
        if (scanResults.files) {
            Object.entries(scanResults.files).forEach(([path, fileData]) => {
                optimized.files[path] = {
                    functions: fileData.functions || [],
                    variables: fileData.variables || [],
                    classes: fileData.classes || [],
                    events: fileData.events || [],
                    workers: fileData.workers || [],
                    imports: fileData.imports || [],
                    exports: fileData.exports || []
                };
            });
        }

        // Optimize graph data
        if (scanResults.graph) {
            optimized.graph.nodes = (scanResults.graph.nodes || []).map(node => ({
                id: node.id,
                path: node.path,
                name: node.name,
                type: node.type,
                category: node.category,
                size: node.size,
                complexity: node.complexity,
                connectivity: node.connectivity,
                functionsCount: node.functionsCount,
                classesCount: node.classesCount,
                variablesCount: node.variablesCount,
                importsCount: node.importsCount,
                exportsCount: node.exportsCount,
                eventsCount: node.eventsCount
                // Remove large arrays like functions, variables etc. as they're in files
            }));

            optimized.graph.edges = scanResults.graph.edges || [];
        }

        // Note: We intentionally exclude fileContents to save space
        // File contents can be re-read if needed for code preview

        return optimized;
    }

    /**
     * Retrieve the most recent scan results for a project
     */
    async getLatestScanResults(projectPath = 'default') {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const index = store.index('projectPath');

        return new Promise((resolve, reject) => {
            const request = index.getAll(projectPath);

            request.onsuccess = () => {
                const results = request.result;
                if (results.length === 0) {
                    resolve(null);
                    return;
                }

                // Sort by timestamp and return the most recent
                results.sort((a, b) => b.timestamp - a.timestamp);
                resolve(results[0].data);
            };

            request.onerror = () => {
                console.error('Failed to retrieve scan results:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Clean up old and large entries
     */
    async cleanup() {
        if (!this.isInitialized) return;

        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.getAll();

            request.onsuccess = async () => {
                const allEntries = request.result;
                const now = Date.now();
                let totalSize = 0;
                const entriesToDelete = [];

                // Calculate total size and identify old entries
                allEntries.forEach(entry => {
                    totalSize += entry.size || 0;
                    
                    // Mark old entries for deletion
                    if (now - entry.timestamp > this.maxAge) {
                        entriesToDelete.push(entry.id);
                    }
                });

                // If total size exceeds limit, delete oldest entries
                if (totalSize > this.maxStorageSize) {
                    allEntries.sort((a, b) => a.timestamp - b.timestamp);
                    let sizeToRemove = totalSize - this.maxStorageSize;
                    
                    for (const entry of allEntries) {
                        if (sizeToRemove <= 0) break;
                        if (!entriesToDelete.includes(entry.id)) {
                            entriesToDelete.push(entry.id);
                            sizeToRemove -= entry.size || 0;
                        }
                    }
                }

                // Delete marked entries
                const deletePromises = entriesToDelete.map(id => {
                    return new Promise((deleteResolve, deleteReject) => {
                        const deleteRequest = store.delete(id);
                        deleteRequest.onsuccess = () => deleteResolve();
                        deleteRequest.onerror = () => deleteReject(deleteRequest.error);
                    });
                });

                try {
                    await Promise.all(deletePromises);
                    if (entriesToDelete.length > 0) {
                        console.log(`IndexedDB cleanup: Removed ${entriesToDelete.length} old entries`);
                    }
                    resolve();
                } catch (error) {
                    console.error('IndexedDB cleanup failed:', error);
                    reject(error);
                }
            };

            request.onerror = () => {
                console.error('Failed to retrieve entries for cleanup:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Clear all stored data
     */
    async clearAll() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.clear();

            request.onsuccess = () => {
                console.log('IndexedDB cleared successfully');
                resolve();
            };

            request.onerror = () => {
                console.error('Failed to clear IndexedDB:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Estimate the size of an object in bytes
     */
    estimateSize(obj) {
        try {
            return new Blob([JSON.stringify(obj)]).size;
        } catch (error) {
            console.warn('Failed to estimate object size:', error);
            return 0;
        }
    }

    /**
     * Get storage statistics
     */
    async getStats() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);

        return new Promise((resolve, reject) => {
            const request = store.getAll();

            request.onsuccess = () => {
                const entries = request.result;
                let totalSize = 0;
                let oldestTimestamp = Date.now();
                let newestTimestamp = 0;

                entries.forEach(entry => {
                    totalSize += entry.size || 0;
                    oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);
                    newestTimestamp = Math.max(newestTimestamp, entry.timestamp);
                });

                resolve({
                    entryCount: entries.length,
                    totalSize,
                    oldestEntry: oldestTimestamp,
                    newestEntry: newestTimestamp,
                    maxStorageSize: this.maxStorageSize,
                    utilizationPercentage: Math.round((totalSize / this.maxStorageSize) * 100)
                });
            };

            request.onerror = () => {
                console.error('Failed to get storage stats:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * Close the database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            this.isInitialized = false;
        }
    }
}

export default IndexedDBStorage;
