/**
 * Web Worker for scanning dependencies
 * Handles file parsing and dependency extraction in a separate thread
 * Enhanced with safety features and timeout protection
 */

// Import SafeRegexProcessor for timeout-protected parsing
// Note: In a worker context, we'll implement the SafeRegexProcessor inline
// since ES6 imports may not be available in all worker contexts

// Global error handler for the worker
self.onerror = function(message, source, lineno, colno, error) {
    const errorMessage = typeof message === 'string' ? message : (error && error.message ? error.message : 'Unknown worker error');
    self.postMessage({
        type: 'error',
        message: `Worker script error: ${errorMessage}`,
        details: `At ${source}:${lineno}:${colno}. Stack: ${error ? error.stack : 'N/A'}`
    });
    return true;
};

// Enhanced error handling with timeout protection
let processingTimeout = null;
const PROCESSING_TIMEOUT = 30000; // 30 seconds per file
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB max file size
const MAX_REGEX_TIME = 5000; // 5 seconds max regex processing time

// Inline SafeRegexProcessor for worker context
class WorkerSafeRegexProcessor {
    constructor(timeout = MAX_REGEX_TIME) {
        this.timeout = timeout;
        this.stats = { timeouts: 0, processed: 0 };
    }

    async processWithTimeout(content, pattern, patternName) {
        if (!content || typeof content !== 'string') return [];
        
        // Check file size
        if (content.length > MAX_FILE_SIZE) {
            console.warn(`File too large for processing: ${content.length} chars`);
            return this.fallbackParsing(content, patternName);
        }

        try {
            return await Promise.race([
                this.applyPattern(content, pattern, patternName),
                this.createTimeoutPromise(patternName)
            ]);
        } catch (error) {
            if (error.name === 'TimeoutError') {
                this.stats.timeouts++;
                console.warn(`Regex timeout for pattern ${patternName}`);
                return this.fallbackParsing(content, patternName);
            }
            throw error;
        } finally {
            this.stats.processed++;
        }
    }

    async applyPattern(content, pattern, patternName) {
        const matches = [];
        const maxMatches = 1000;
        let matchCount = 0;
        let match;

        if (pattern.global) pattern.lastIndex = 0;

        while ((match = pattern.exec(content)) !== null && matchCount < maxMatches) {
            matches.push({
                match: match[0],
                groups: Array.from(match).slice(1),
                index: match.index,
                line: this.getLineNumber(content, match.index)
            });
            matchCount++;

            if (!pattern.global) break;
            if (matchCount % 100 === 0) await this.yieldControl();
        }

        if (pattern.global) pattern.lastIndex = 0;
        return matches;
    }

    /**
     * Get line number from character index
     * @param {string} content - Content string
     * @param {number} index - Character index
     * @returns {number} - Line number (1-based)
     */
    getLineNumber(content, index) {
        if (index < 0 || index >= content.length) return 1;

        // Cache line breaks for performance on large files
        if (!this.lineBreakCache || this.lineBreakCache.content !== content) {
            this.lineBreakCache = {
                content: content,
                breaks: []
            };

            // Build line break index
            for (let i = 0; i < content.length; i++) {
                if (content[i] === '\n') {
                    this.lineBreakCache.breaks.push(i);
                }
            }
        }

        // Binary search for line number
        const breaks = this.lineBreakCache.breaks;
        let left = 0;
        let right = breaks.length - 1;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            if (breaks[mid] < index) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return left + 1; // +1 because line numbers are 1-based
    }

    fallbackParsing(content, patternName) {
        // Simple string-based fallback for common patterns
        const lines = content.split('\n');
        const results = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const lineNumber = i + 1;

            if (patternName.includes('import') && line.includes('import ')) {
                results.push({
                    match: line,
                    groups: [line],
                    index: i,
                    line: lineNumber
                });
            } else if (patternName.includes('require') && line.includes('require(')) {
                results.push({
                    match: line,
                    groups: [line],
                    index: i,
                    line: lineNumber
                });
            } else if (patternName.includes('export') && line.includes('export ')) {
                results.push({
                    match: line,
                    groups: [line],
                    index: i,
                    line: lineNumber
                });
            } else if (patternName.includes('function') && (line.includes('function ') || line.includes(') => '))) {
                results.push({
                    match: line,
                    groups: [line],
                    index: i,
                    line: lineNumber
                });
            } else if (patternName.includes('class') && line.includes('class ')) {
                results.push({
                    match: line,
                    groups: [line],
                    index: i,
                    line: lineNumber
                });
            }

            if (i % 1000 === 0) break; // Limit fallback processing
        }

        return results;
    }

    createTimeoutPromise(patternName) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                const error = new Error(`Regex timeout: ${patternName}`);
                error.name = 'TimeoutError';
                reject(error);
            }, this.timeout);
        });
    }

    async yieldControl() {
        return new Promise(resolve => setTimeout(resolve, 0));
    }
}

// Worker state
const state = {
    projectRootPath: '',
    files: [],
    processedFiles: 0,
    totalFiles: 0,
    excludeRules: [],
    results: {
        nodes: [],
        links: [],
        metadata: {}
    },
    safeRegexProcessor: new WorkerSafeRegexProcessor(),
    memoryUsage: { peak: 0, current: 0 }
};

// Regular expressions for dependency detection
const patterns = {
    // JavaScript imports and requires - enhanced to be more robust
    jsImport: /import\s+(?:(?:(\*\s+as\s+)?([a-zA-Z0-9_$]+)|\{([^}]+)\})\s+from\s+)?['"]([^'"]+)['"]/g,
    // Additional simpler import pattern to catch basic imports
    jsBasicImport: /import\s+.*?['"]([^'"]+)['"]|import\(['"]([^'"]+)['"]/g,
    jsRequire: /(?:const|let|var)\s+(?:([a-zA-Z0-9_$]+)|\{([^}]+)\})\s*=\s*require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    jsRequireSimple: /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    dynamicImport: /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    // JavaScript exports - enhanced for better coverage
    jsExport: /export\s+(default\s+)?(?:async\s+)?(function\*?|class|let|const|var)\s+([a-zA-Z0-9_$]+)/g,
    jsExportFrom: /export\s+(?:\{([^}]+)\})\s+from\s+['"]([^'"]+)['"]/g,
    jsExportAll: /export\s+\*\s+from\s+['"]([^'"]+)['"]/g,
    // Additional export patterns
    jsExportDestructured: /export\s+(?:const|let|var)?\s*\{\s*([^}]+)\s*\}/g,
    jsExportConstVar: /export\s+(const|let|var)\s+([a-zA-Z0-9_$]+)/g,
    
    // TypeScript specific patterns
    tsImportType: /import\s+type\s+(?:(?:(\*\s+as\s+)?([a-zA-Z0-9_$]+)|\{([^}]+)\})\s+from\s+)?['"]([^'"]+)['"]/g,
    tsExportType: /export\s+type\s+([a-zA-Z0-9_$]+)/g,
    tsInterface: /(?:export\s+)?interface\s+([a-zA-Z0-9_$]+)/g,
    
    // Function and class detection
    function: /(?:export\s+)?(?:async\s+)?function\s+([a-zA-Z0-9_$]+)/g,
    arrowFunction: /(?:export\s+)?(?:const|let|var)\s+([a-zA-Z0-9_$]+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>/g,
    class: /(?:export\s+)?class\s+([a-zA-Z0-9_$]+)/g,
    
    // CSS imports
    cssImport: /@import\s+(?:url\()?\s*['"]([^'"]+)['"]\s*\)?/g,
    
    // HTML script and link tags
    htmlScript: /<script[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/g,
    htmlLink: /<link[^>]*href\s*=\s*['"]([^'"]+)['"][^>]*>/g,
    
    // Event patterns - enhanced for better coverage
    eventListener: /addEventListener\s*\(\s*['"]([^'"]+)['"]/g,
    eventDispatcher: /(?:dispatchEvent|emit|trigger)\s*\(\s*(?:new\s+(?:CustomEvent|Event)\s*\(\s*)?['"]([^'"]+)['"]/g,
    eventEmitter: /\.(?:on|once|emit)\s*\(\s*['"]([^'"]+)['"]/g,
    // Additional event patterns
    domOnEvent: /\.on([a-zA-Z]+)\s*=/g,
    jqueryEvent: /\$\([^)]*\)\.(?:on|bind|delegate)\s*\(\s*['"]([^'"]+)['"]/g,
    
    // Worker patterns - enhanced for better coverage
    workerCreation: /new\s+Worker\s*\(\s*['"]([^'"]+)['"]/g,
    workerImport: /importScripts\s*\(\s*['"]([^'"]+)['"]/g,
    sharedWorker: /new\s+SharedWorker\s*\(\s*['"]([^'"]+)['"]/g,
    serviceWorker: /(?:navigator\.serviceWorker|ServiceWorkerContainer)\.register\s*\(\s*['"]([^'"]+)['"]/g,
    // Additional worker patterns
    workerVariable: /(?:var|let|const)\s+([a-zA-Z0-9_$]+)\s*=\s*new\s+Worker\s*\(/g,
    dynamicWorkerCreation: /(?:createWorker|initWorker|setupWorker|startWorker)\s*\([^)]*['"]([^'"]+)['"]/g,
    
    // Variable declarations - enhanced for better coverage
    varDeclaration: /\b(?:var|let|const)\s+([a-zA-Z0-9_$]+)/g,
    varAssignment: /\b([a-zA-Z0-9_$]+)\s*=\s*(?!function|class|new)/g,
    varWithValue: /\b(?:var|let|const)\s+([a-zA-Z0-9_$]+)\s*=\s*([^;]+)/g,
    destructuringVar: /(?:const|let|var)\s+\{([^}]+)\}\s*=/g,
    arrayDestructuring: /(?:const|let|var)\s+\[([^\]]+)\]\s*=/g,
    objectProperty: /([a-zA-Z0-9_$]+)\s*:\s*(?:function|async\s+function|\([^)]*\)\s*=>)/g,
    
    // Module patterns
    dynamicImport: /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    moduleExports: /module\.exports\s*=\s*([a-zA-Z0-9_$]+|\{)/g,
    exportsProperty: /exports\.([a-zA-Z0-9_$]+)\s*=/g,
    // Additional ES6 export patterns
    es6DefaultExport: /export\s+default\s+([a-zA-Z0-9_$]+)/g,
    es6NamedExport: /export\s+(?:const|let|var|function|class)\s+([a-zA-Z0-9_$]+)/g,
};

/**
 * Normalize a path by converting backslashes to forward slashes
 * @param {string} pathStr - Path to normalize
 * @param {string} rootStr - Optional root path for relative paths
 * @returns {string} - Normalized path
 */
function normalizePath(pathStr, rootStr = '') {
    if (typeof pathStr !== 'string') pathStr = '';
    if (typeof rootStr !== 'string') rootStr = '';
    
    let s = pathStr.replace(/\\/g, '/');
    let r = rootStr.replace(/\\/g, '/');
    
    if (r && r.length > 0 && !r.endsWith('/')) { 
        r += '/'; 
    }
    
    let finalPath = s;
    if (r && s.startsWith('.') && !s.startsWith(r)) { 
        finalPath = r + s; 
    }
    
    const parts = finalPath.split('/');
    const stack = [];
    
    for (const part of parts) {
        if (part === '.' || (part === '' && stack.length > 0 && stack[stack.length -1] !== '')) { 
            continue; 
        }
        
        if (part === '..') {
            if (stack.length > 0 && stack[stack.length - 1] !== '..') { 
                stack.pop(); 
            }
            else if (!r || r.trim() === '') { 
                stack.push('..'); 
            }
        } else { 
            stack.push(part); 
        }
    }
    
    return stack.join('/');
}

/**
 * Check if a path should be excluded based on exclude rules
 * @param {string} path - Path to check
 * @returns {boolean} - True if path should be excluded
 */
function shouldExclude(path) {
    if (!path) return true;
    
    // Ensure excludeRules is an array
    if (!Array.isArray(state.excludeRules)) {
        console.warn('Worker: excludeRules is not an array, initializing as empty array');
        state.excludeRules = [];
        return false;
    }
    
    for (const rule of state.excludeRules) {
        if (typeof rule === 'string' && path.includes(rule)) {
            return true;
        } else if (rule instanceof RegExp && rule.test(path)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Resolve a relative import path to an absolute path
 * @param {string} importPath - Import path to resolve
 * @param {string} currentFilePath - Path of the current file
 * @returns {string} - Resolved path
 */
function resolveImportPath(importPath, currentFilePath) {
    // Handle node modules
    if (isNodeModule(importPath)) {
        return importPath;
    }
    
    // Get directory of current file
    const currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/') + 1);
    
    // Resolve relative path
    return normalizePath(importPath, currentDir);
}

/**
 * Check if a path is a node module
 * @param {string} path - Path to check
 * @returns {boolean} - True if path is a node module
 */
function isNodeModule(path) {
    if (!path) return false;
    
    if (!path.startsWith('./') && !path.startsWith('../') && 
        !path.includes(':') && !path.startsWith('/')) {
        const lastSegment = path.split('/').pop();
        if (lastSegment && !lastSegment.includes('.')) {
            return true;
        }
    }
    
    const nodeModulePrefixes = ['@', 'node:', 'npm:'];
    for (const prefix of nodeModulePrefixes) {
        if (path.startsWith(prefix)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Extract file extension from path
 * @param {string} path - File path
 * @returns {string} - File extension (without the dot)
 */
function getFileExtension(path) {
    if (!path) return '';
    
    const dotIndex = path.lastIndexOf('.');
    if (dotIndex === -1) return '';
    
    return path.substring(dotIndex + 1).toLowerCase();
}

/**
 * Parse a JavaScript or TypeScript file to extract dependencies
 * Enhanced with timeout protection and error handling
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Promise<Object>} - Extracted dependencies and metadata
 */
async function parseJavaScript(content, filePath) {
    const logPrefix = `[${filePath}]`;
    const result = {
        imports: [],
        exports: [],
        functions: [],
        classes: [],
        variables: [],
        events: [],
        workers: []
    };
    
    try {
        // Check content size and validity
        if (!content || typeof content !== 'string') {
            console.warn(`Invalid content for ${filePath}`);
            return result;
        }
        
        // DEBUG: Log file parsing details (reduced verbosity)
        if (content.includes('import') || content.includes('export') || content.includes('function') || content.includes('class')) {
            console.log(`${logPrefix} === PARSING (${content.length} chars) ===`);
        }
        
        if (content.length > MAX_FILE_SIZE) {
            console.warn(`File ${filePath} too large (${content.length} chars), using fallback parsing`);
            return await parseJavaScriptFallback(content, filePath);
        }
        
        // Process patterns with timeout protection - Enhanced with more patterns
        const patternProcessors = [
            // Import patterns
            { pattern: patterns.jsImport, name: 'jsImport', processor: processImportMatch },
            { pattern: patterns.jsBasicImport, name: 'jsBasicImport', processor: processImportMatch },
            { pattern: patterns.jsRequire, name: 'jsRequire', processor: processRequireMatch },
            { pattern: patterns.jsRequireSimple, name: 'jsRequireSimple', processor: processSimpleRequireMatch },
            { pattern: patterns.dynamicImport, name: 'dynamicImport', processor: processDynamicImportMatch },
            
            // Export patterns
            { pattern: patterns.jsExport, name: 'jsExport', processor: processExportMatch },
            { pattern: patterns.jsExportFrom, name: 'jsExportFrom', processor: processExportFromMatch },
            { pattern: patterns.jsExportAll, name: 'jsExportAll', processor: processExportAllMatch },
            { pattern: patterns.moduleExports, name: 'moduleExports', processor: processModuleExportsMatch },
            { pattern: patterns.exportsProperty, name: 'exportsProperty', processor: processExportsPropertyMatch },
            { pattern: patterns.es6DefaultExport, name: 'es6DefaultExport', processor: processExportMatch },
            { pattern: patterns.es6NamedExport, name: 'es6NamedExport', processor: processExportMatch },
            { pattern: patterns.jsExportDestructured, name: 'jsExportDestructured', processor: processExportFromMatch },
            { pattern: patterns.jsExportConstVar, name: 'jsExportConstVar', processor: processExportMatch },
            
            // Function and class patterns
            { pattern: patterns.function, name: 'function', processor: processFunctionMatch },
            { pattern: patterns.arrowFunction, name: 'arrowFunction', processor: processArrowFunctionMatch },
            { pattern: patterns.class, name: 'class', processor: processClassMatch },
            
            // Event patterns
            { pattern: patterns.eventListener, name: 'eventListener', processor: processEventMatch },
            { pattern: patterns.eventDispatcher, name: 'eventDispatcher', processor: processEventMatch },
            { pattern: patterns.eventEmitter, name: 'eventEmitter', processor: processEventMatch },
            { pattern: patterns.domOnEvent, name: 'domOnEvent', processor: processEventMatch },
            { pattern: patterns.jqueryEvent, name: 'jqueryEvent', processor: processEventMatch },
            
            // Worker patterns
            { pattern: patterns.workerCreation, name: 'workerCreation', processor: processWorkerMatch },
            { pattern: patterns.workerImport, name: 'workerImport', processor: processWorkerImportMatch },
            { pattern: patterns.sharedWorker, name: 'sharedWorker', processor: processWorkerMatch },
            { pattern: patterns.serviceWorker, name: 'serviceWorker', processor: processWorkerMatch },
            { pattern: patterns.workerVariable, name: 'workerVariable', processor: processWorkerMatch },
            { pattern: patterns.dynamicWorkerCreation, name: 'dynamicWorkerCreation', processor: processWorkerMatch },
            
            // Variable patterns
            { pattern: patterns.varDeclaration, name: 'varDeclaration', processor: processVariableMatch },
            { pattern: patterns.varAssignment, name: 'varAssignment', processor: processVariableMatch },
            { pattern: patterns.varWithValue, name: 'varWithValue', processor: processVariableMatch },
            { pattern: patterns.destructuringVar, name: 'destructuringVar', processor: processDestructuringMatch },
            { pattern: patterns.arrayDestructuring, name: 'arrayDestructuring', processor: processArrayDestructuringMatch },
            { pattern: patterns.objectProperty, name: 'objectProperty', processor: processObjectPropertyMatch }
        ];
        
        // Process each pattern with timeout protection
        for (const { pattern, name, processor } of patternProcessors) {
            try {
                const matches = await state.safeRegexProcessor.processWithTimeout(content, pattern, name);
                
                if (matches.length > 0) {
                    console.log(`${logPrefix} Pattern ${name} found ${matches.length} matches.`);
                }
                
                for (const match of matches) {
                    const processed = processor(match, filePath, content);
                    if (processed) {
                        const category = getCategoryForPattern(name);
                        if (result[category]) {
                            result[category].push(processed);
                            // console.log(`${logPrefix} Added to ${category} (Pattern: ${name}):`, JSON.stringify(processed)); // Stringify can be too verbose for large objects
                            console.log(`${logPrefix} Added to ${category} (Pattern: ${name}): item type ${typeof processed}`);
                        } else {
                            console.warn(`${logPrefix} Unknown category ${category} for pattern ${name}`);
                        }
                    } else if (matches.length > 0) { // Only log if there was a match but processor returned null
                        console.log(`${logPrefix} Processor for ${name} returned no data for match:`, JSON.stringify(match.match)); // Log the matched string
                    }
                }
                
                // Yield control between patterns
                await yieldControl();
                
            } catch (error) {
                console.error(`${logPrefix} Error processing pattern ${name}:`, error);
            }
        }
        
        console.log(`${logPrefix} === FINAL PARSED RESULT ===`, JSON.stringify(result));
        return result;
        
    } catch (error) {
        console.error(`Error parsing JavaScript file ${filePath}:`, error);
        // Return partial results
    }
    
    return result;
}

/**
 * Fallback parsing for large or problematic files
 */
async function parseJavaScriptFallback(content, filePath) {
    const result = {
        imports: [],
        exports: [],
        functions: [],
        classes: [],
        variables: [],
        events: [],
        workers: []
    };
    
    try {
        const lines = content.split('\n');
        const maxLines = Math.min(lines.length, 5000); // Limit processing
        
        for (let i = 0; i < maxLines; i++) {
            const line = lines[i].trim();
            
            // Simple string-based detection
            const lineNumber = i + 1;

            if (line.includes('import ') && line.includes(' from ')) {
                const importMatch = line.match(/import\s+.*?\s+from\s+['"]([^'"]+)['"]/);
                if (importMatch) {
                    result.imports.push({
                        path: resolveImportPath(importMatch[1], filePath),
                        isDefault: false,
                        isNamespace: false,
                        names: [],
                        line: lineNumber,
                        fallback: true
                    });
                }
            }

            if (line.includes('require(')) {
                const requireMatch = line.match(/require\s*\(\s*['"]([^'"]+)['"]\s*\)/);
                if (requireMatch) {
                    result.imports.push({
                        path: resolveImportPath(requireMatch[1], filePath),
                        isDefault: false,
                        isNamespace: false,
                        names: [],
                        line: lineNumber,
                        fallback: true
                    });
                }
            }

            if (line.startsWith('export ')) {
                const exportMatch = line.match(/export\s+(?:default\s+)?(?:function|class|const|let|var)\s+([a-zA-Z0-9_$]+)/);
                if (exportMatch) {
                    result.exports.push({
                        name: exportMatch[1],
                        isDefault: line.includes('default'),
                        type: 'unknown',
                        line: lineNumber,
                        fallback: true
                    });
                }
            }

            if (line.includes('function ')) {
                const funcMatch = line.match(/function\s+([a-zA-Z0-9_$]+)/);
                if (funcMatch) {
                    result.functions.push({
                        name: funcMatch[1],
                        type: 'function',
                        line: lineNumber,
                        fallback: true
                    });
                }
            }

            if (line.includes('class ')) {
                const classMatch = line.match(/class\s+([a-zA-Z0-9_$]+)/);
                if (classMatch) {
                    result.classes.push({
                        name: classMatch[1],
                        type: 'class',
                        line: lineNumber,
                        fallback: true
                    });
                }
            }

            // Add variable detection to fallback parsing
            if (line.includes('const ') || line.includes('let ') || line.includes('var ')) {
                const varMatch = line.match(/\b(?:const|let|var)\s+([a-zA-Z0-9_$]+)/);
                if (varMatch) {
                    const varType = line.includes('const ') ? 'const' :
                                   line.includes('let ') ? 'let' : 'var';
                    result.variables.push({
                        name: varMatch[1],
                        type: varType,
                        line: lineNumber,
                        originalMatch: line.trim(),
                        fallback: true
                    });
                }
            }
            
            // Yield control every 1000 lines
            if (i % 1000 === 0) {
                await yieldControl();
            }
        }
        
    } catch (error) {
        console.error(`Fallback parsing failed for ${filePath}:`, error);
    }
    
    return result;
}

// Pattern match processors
function processImportMatch(match, filePath, content) {
    // Match object format from SafeRegexProcessor: {match, groups, index, line}
    // Ensure fallback handling if match structure is unexpected
    if (!match || !match.match) {
        console.warn(`Invalid match object in processImportMatch for ${filePath}`);
        return null;
    }

    // For import pattern: import {a, b} from 'path'
    // or import * as name from 'path'
    // or import name from 'path'
    const importMatch = match.match;
    const groups = match.groups || [];
    const lineNumber = match.line || 1;

    // Extract the import path - typically the last quoted string
    let importPath = null;
    if (importMatch.includes('from')) {
        const pathMatch = importMatch.match(/['"]([^'"]+)['"]/);
        if (pathMatch && pathMatch[1]) {
            importPath = pathMatch[1];
        }
    }
    
    // If no path found, can't process this import
    if (!importPath) {
        return null;
    }
    
    // Determine import type and names
    const isNamespace = importMatch.includes('* as');
    const hasNamedImports = importMatch.includes('{') && importMatch.includes('}');
    const hasDefaultImport = !isNamespace && !importMatch.startsWith('import {') && importMatch.includes('from');
    
    // Extract names based on type
    let names = [];
    if (hasNamedImports) {
        // Extract content between braces
        const namedMatch = importMatch.match(/\{([^}]+)\}/);
        if (namedMatch && namedMatch[1]) {
            names = namedMatch[1].split(',').map(name => name.trim());
        }
    }
    
    // Extract default import name
    let defaultName = null;
    if (hasDefaultImport) {
        const defaultMatch = importMatch.match(/import\s+([^{*\s,]+)/);
        if (defaultMatch && defaultMatch[1]) {
            defaultName = defaultMatch[1];
        }
    }
    
    // Extract namespace name
    let namespaceName = null;
    if (isNamespace) {
        const nsMatch = importMatch.match(/\*\s+as\s+([^\s,]+)/);
        if (nsMatch && nsMatch[1]) {
            namespaceName = nsMatch[1];
        }
    }
    
    return {
        path: resolveImportPath(importPath, filePath),
        rawPath: importPath,
        isDefault: hasDefaultImport,
        isNamespace: isNamespace,
        defaultName: defaultName,
        namespaceName: namespaceName,
        names: names,
        originalMatch: importMatch,
        line: lineNumber
    };
}

function processRequireMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, defaultImport, namedImports, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            isDefault: !!defaultImport,
            isNamespace: false,
            names: namedImports ? namedImports.split(',').map(name => name.trim()) : [],
            line: lineNumber
        };
    }
    return null;
}

function processSimpleRequireMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            isDefault: false,
            isNamespace: false,
            names: [],
            line: lineNumber
        };
    }
    return null;
}

function processExportMatch(match, filePath, content) {
    if (!match || !match.match) {
        console.warn(`Invalid match object in processExportMatch for ${filePath}`);
        return null;
    }

    const exportMatch = match.match;
    const groups = match.groups || [];
    const lineNumber = match.line || 1;

    // Handle different export formats
    // export default function name() {}
    // export class Name {}
    // export const name = value

    // Check if it's a default export
    const isDefault = exportMatch.includes('export default');

    // Extract the exported item's name
    let name = null;
    let type = 'variable'; // default type
    
    if (exportMatch.includes('function')) {
        type = 'function';
        const funcMatch = exportMatch.match(/function\s+([a-zA-Z0-9_$]+)/);
        if (funcMatch && funcMatch[1]) {
            name = funcMatch[1];
        }
    } else if (exportMatch.includes('class')) {
        type = 'class';
        const classMatch = exportMatch.match(/class\s+([a-zA-Z0-9_$]+)/);
        if (classMatch && classMatch[1]) {
            name = classMatch[1];
        }
    } else if (exportMatch.includes('const') || exportMatch.includes('let') || exportMatch.includes('var')) {
        type = 'variable';
        const varMatch = exportMatch.match(/(?:const|let|var)\s+([a-zA-Z0-9_$]+)/);
        if (varMatch && varMatch[1]) {
            name = varMatch[1];
        }
    } else if (isDefault) {
        // Handle export default objectOrExpression
        const defaultMatch = exportMatch.match(/export\s+default\s+([a-zA-Z0-9_$]+)/);
        if (defaultMatch && defaultMatch[1]) {
            name = defaultMatch[1];
            type = 'expression';
        }
    }
    
    // If couldn't extract a name, look for any identifier
    if (!name) {
        const identMatch = exportMatch.match(/[\s{]([a-zA-Z0-9_$]+)[\s,}]/);
        if (identMatch && identMatch[1]) {
            name = identMatch[1];
        }
    }
    
    // If still no name but we have an export, create a generic entry
    if (!name && exportMatch.includes('export')) {
        name = isDefault ? 'default' : 'unnamed';
    }
    
    if (name) {
        return {
            name,
            isDefault: isDefault,
            type: type,
            originalMatch: exportMatch,
            line: lineNumber
        };
    }
    
    return null;
}

function processExportFromMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, namedExports, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            names: namedExports ? namedExports.split(',').map(name => name.trim()) : [],
            line: lineNumber
        };
    }
    return null;
}

function processExportAllMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            isAll: true,
            line: lineNumber
        };
    }
    return null;
}

function processFunctionMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            type: 'function',
            line: lineNumber
        };
    }
    return null;
}

function processArrowFunctionMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            type: 'arrow',
            line: lineNumber
        };
    }
    return null;
}

function processClassMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            type: 'class',
            line: lineNumber
        };
    }
    return null;
}

function processEventMatch(match, filePath, content) {
    if (!match || !match.match) {
        console.warn(`Invalid match object in processEventMatch for ${filePath}`);
        return null;
    }

    const eventMatch = match.match;
    const lineNumber = match.line || 1;

    // Extract event name from various patterns like:
    // addEventListener('click', handler)
    // emit('eventName', data)
    // dispatchEvent(new CustomEvent('event'))

    let eventName = null;
    let eventType = 'unknown';
    
    if (eventMatch.includes('addEventListener')) {
        eventType = 'listener';
        const nameMatch = eventMatch.match(/addEventListener\s*\(\s*['"]([^'"]+)/);
        if (nameMatch && nameMatch[1]) {
            eventName = nameMatch[1];
        }
    } else if (eventMatch.includes('dispatchEvent') || eventMatch.includes('CustomEvent') || eventMatch.includes('Event(')) {
        eventType = 'dispatcher';
        const nameMatch = eventMatch.match(/['"]([^'"]+)/);
        if (nameMatch && nameMatch[1]) {
            eventName = nameMatch[1];
        }
    } else if (eventMatch.includes('.on') || eventMatch.includes('.once') || eventMatch.includes('.emit')) {
        eventType = eventMatch.includes('.emit') ? 'emitter' : 'handler';
        const nameMatch = eventMatch.match(/\.(on|once|emit)\s*\(\s*['"]([^'"]+)/);
        if (nameMatch && nameMatch[2]) {
            eventName = nameMatch[2];
        }
    }
    
    if (eventName) {
        return {
            name: eventName,
            type: eventType,
            originalMatch: eventMatch,
            line: lineNumber
        };
    }
    
    return null;
}

function processVariableMatch(match, filePath, content) {
    if (!match || !match.match) {
        console.warn(`Invalid match object in processVariableMatch for ${filePath}`);
        return null;
    }

    const varMatch = match.match;
    const lineNumber = match.line || 1;

    // Extract variable name and declaration type
    // const x = value;
    // let y = value;
    // var z = value;

    let varName = null;
    let varType = 'var'; // default
    
    if (varMatch.includes('const ')) {
        varType = 'const';
    } else if (varMatch.includes('let ')) {
        varType = 'let';
    } else if (varMatch.includes('var ')) {
        varType = 'var';
    }
    
    // Extract variable name
    let nameMatch = null;
    if (varType === 'const') {
        nameMatch = varMatch.match(/const\s+([a-zA-Z0-9_$]+)/);
    } else if (varType === 'let') {
        nameMatch = varMatch.match(/let\s+([a-zA-Z0-9_$]+)/);
    } else { // var
        nameMatch = varMatch.match(/var\s+([a-zA-Z0-9_$]+)/);
    }
    
    if (nameMatch && nameMatch[1]) {
        varName = nameMatch[1];
    }
    
    if (varName) {
        return {
            name: varName,
            type: varType,
            originalMatch: varMatch,
            line: lineNumber
        };
    }
    
    return null;
}

function processDynamicImportMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            isDefault: false,
            isNamespace: false,
            names: [],
            isDynamic: true,
            line: lineNumber
        };
    }
    return null;
}

function processModuleExportsMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            isDefault: true,
            type: 'module.exports',
            line: lineNumber
        };
    }
    return null;
}

function processExportsPropertyMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            isDefault: false,
            type: 'exports.property',
            line: lineNumber
        };
    }
    return null;
}

function processWorkerMatch(match, filePath, content) {
    if (!match || !match.match) {
        console.warn(`Invalid match object in processWorkerMatch for ${filePath}`);
        return null;
    }

    const workerMatch = match.match;
    const lineNumber = match.line || 1;

    // Extract worker path from Worker constructor
    // new Worker('path/to/worker.js')

    let workerPath = null;
    let workerType = 'worker'; // default type

    // Extract quoted path
    const pathMatch = workerMatch.match(/['"]([^'"]+)/);
    if (pathMatch && pathMatch[1]) {
        workerPath = pathMatch[1];
    }

    // Determine worker type
    if (workerMatch.includes('SharedWorker')) {
        workerType = 'shared';
    } else if (workerMatch.includes('ServiceWorker')) {
        workerType = 'service';
    } else {
        workerType = 'worker';
    }

    if (workerPath) {
        return {
            path: resolveImportPath(workerPath, filePath),
            rawPath: workerPath,
            type: workerType,
            originalMatch: workerMatch,
            line: lineNumber
        };
    }

    return null;
}

function processWorkerImportMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, importPath] = groups;
    const lineNumber = match.line || 1;
    if (importPath) {
        return {
            path: resolveImportPath(importPath, filePath),
            type: 'worker-import',
            line: lineNumber
        };
    }
    return null;
}

function processDestructuringMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, destructuredVars] = groups;
    const lineNumber = match.line || 1;
    if (destructuredVars) {
        const names = destructuredVars.split(',').map(name => name.trim().split(':')[0].trim());
        return {
            names,
            type: 'destructuring',
            line: lineNumber
        };
    }
    return null;
}

function processArrayDestructuringMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, destructuredVars] = groups;
    const lineNumber = match.line || 1;
    if (destructuredVars) {
        const names = destructuredVars.split(',').map(name => name.trim()).filter(name => name && !name.includes('...'));
        return {
            names,
            type: 'array-destructuring',
            line: lineNumber
        };
    }
    return null;
}

function processObjectPropertyMatch(match, filePath, content) {
    const groups = match.groups || [];
    const [fullMatch, name] = groups;
    const lineNumber = match.line || 1;
    if (name) {
        return {
            name,
            type: 'object-property',
            line: lineNumber
        };
    }
    return null;
}

function getCategoryForPattern(patternName) {
    const categoryMap = {
        // Import patterns
        jsImport: 'imports',
        jsBasicImport: 'imports',
        jsRequire: 'imports',
        jsRequireSimple: 'imports',
        dynamicImport: 'imports',

        // Export patterns
        jsExport: 'exports',
        jsExportFrom: 'exports',
        jsExportAll: 'exports',
        jsExportDestructured: 'exports',
        jsExportConstVar: 'exports',
        es6DefaultExport: 'exports',
        es6NamedExport: 'exports',
        moduleExports: 'exports',
        exportsProperty: 'exports',

        // Function patterns
        function: 'functions',
        arrowFunction: 'functions',

        // Class patterns
        class: 'classes',

        // Event patterns
        eventListener: 'events',
        eventDispatcher: 'events',
        eventEmitter: 'events',

        // Worker patterns
        workerCreation: 'workers',
        workerImport: 'workers',
        workerVariable: 'workers',
        dynamicWorkerCreation: 'workers',
        sharedWorker: 'workers',
        serviceWorker: 'workers',

        // Variable patterns
        varDeclaration: 'variables',
        varAssignment: 'variables',
        varWithValue: 'variables',
        destructuringVar: 'variables',
        arrayDestructuring: 'variables',
        objectProperty: 'variables'
    };
    return categoryMap[patternName] || 'other';
}

// Utility function for yielding control
async function yieldControl() {
    return new Promise(resolve => setTimeout(resolve, 0));
}

/**
 * Parse a CSS file to extract imports
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Object} - Extracted dependencies and metadata
 */
function parseCSS(content, filePath) {
    const result = {
        imports: [],
        exports: [],
        functions: [],
        classes: [],
        variables: [],
        events: [],
        workers: []
    };

    // Extract imports
    let match;
    while ((match = patterns.cssImport.exec(content)) !== null) {
        const [, importPath] = match;

        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.imports.push({
                path: resolvedPath
            });
        }
    }

    // Extract CSS classes and IDs as "functions" for visualization purposes
    const classPattern = /\.([a-zA-Z0-9_-]+)\s*\{/g;
    const idPattern = /#([a-zA-Z0-9_-]+)\s*\{/g;

    // Extract CSS classes
    while ((match = classPattern.exec(content)) !== null) {
        const [, className] = match;
        if (className) {
            result.functions.push({
                name: `.${className}`,
                type: 'css-class',
                line: content.substring(0, match.index).split('\n').length
            });
        }
    }

    // Extract CSS IDs
    while ((match = idPattern.exec(content)) !== null) {
        const [, idName] = match;
        if (idName) {
            result.functions.push({
                name: `#${idName}`,
                type: 'css-id',
                line: content.substring(0, match.index).split('\n').length
            });
        }
    }

    return result;
}

/**
 * Parse an HTML file to extract script and link tags
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Object} - Extracted dependencies and metadata
 */
function parseHTML(content, filePath) {
    const result = {
        imports: [],
        exports: [],
        functions: [],
        classes: [],
        variables: [],
        events: [],
        workers: [],
        scripts: [],
        links: []
    };

    // Extract script tags
    let match;
    while ((match = patterns.htmlScript.exec(content)) !== null) {
        const [, src] = match;

        if (src) {
            const resolvedPath = resolveImportPath(src, filePath);
            result.scripts.push({
                path: resolvedPath
            });
            // Also add to imports for dependency tracking
            result.imports.push({
                path: resolvedPath,
                type: 'script'
            });
        }
    }

    // Extract link tags (CSS files)
    while ((match = patterns.htmlLink.exec(content)) !== null) {
        const [, href] = match;

        if (href) {
            const resolvedPath = resolveImportPath(href, filePath);
            result.links.push({
                path: resolvedPath
            });
            // Also add to imports for dependency tracking
            result.imports.push({
                path: resolvedPath,
                type: 'stylesheet'
            });
        }
    }

    // Extract HTML elements as "functions" for visualization purposes
    const elementPattern = /<([a-zA-Z][a-zA-Z0-9]*)[^>]*>/g;
    const elementCounts = {};

    while ((match = elementPattern.exec(content)) !== null) {
        const [, tagName] = match;
        if (tagName && !['html', 'head', 'body', 'meta', 'title'].includes(tagName.toLowerCase())) {
            elementCounts[tagName] = (elementCounts[tagName] || 0) + 1;
        }
    }

    // Add unique elements as functions
    Object.entries(elementCounts).forEach(([tagName, count]) => {
        result.functions.push({
            name: `<${tagName}>`,
            type: 'html-element',
            count: count
        });
    });

    return result;
}

/**
 * Process a file and extract dependencies with enhanced error handling
 * @param {Object} file - File object with path and content
 * @returns {Promise<Object>} - Processed file with dependencies
 */
async function processFile(file) {
    if (!file || !file.path || shouldExclude(file.path)) {
        return null;
    }
    
    const startTime = Date.now();
    
    try {
        // Validate file
        if (!file.content || typeof file.content !== 'string') {
            // Don't log warnings for binary files or files with no content
            if (file.content === null || file.content === undefined) {
                return createEmptyFileResult(file.path);
            }

            // Provide more specific warning based on content type
            const contentType = typeof file.content;
            const isExternFile = file.path.includes('.externs.js');

            if (isExternFile) {
                // .externs.js files are Google Closure Compiler extern files, often have special content
                console.debug(`Skipping .externs.js file (likely Google Closure extern): ${file.path}`);
            } else {
                console.warn(`Invalid file content for ${file.path} (content type: ${contentType})`);
            }

            return createEmptyFileResult(file.path);
        }
        
        // Skip binary files and very large files silently
        if (file.content.length === 0) {
            return createEmptyFileResult(file.path);
        }
        
        // Check file size
        if (file.content.length > MAX_FILE_SIZE) {
            console.warn(`File ${file.path} exceeds size limit (${file.content.length} chars)`);
            // Still process but with fallback methods
        }
        
        // Monitor memory usage
        updateMemoryUsage();
        
        const extension = getFileExtension(file.path);
        let dependencies = {};
        
        // Set processing timeout
        const timeoutPromise = new Promise((_, reject) => {
            processingTimeout = setTimeout(() => {
                reject(new Error(`File processing timeout: ${file.path}`));
            }, PROCESSING_TIMEOUT);
        });
        
        try {
            const processingPromise = (async () => {
                switch (extension) {
                    case 'js':
                    case 'mjs':
                    case 'jsx':
                    case 'ts':
                    case 'tsx':
                        dependencies = await parseJavaScript(file.content, file.path);
                        break;

                    case 'css':
                    case 'scss':
                    case 'sass':
                    case 'less':
                        dependencies = await parseCSS(file.content, file.path);
                        break;

                    case 'html':
                    case 'htm':
                        dependencies = await parseHTML(file.content, file.path);
                        break;

                    case 'json':
                        // JSON files don't have dependencies but should be included in the graph
                        dependencies = {
                            imports: [],
                            exports: [],
                            functions: [],
                            classes: [],
                            variables: [],
                            events: [],
                            workers: []
                        };
                        break;

                    default:
                        // Unsupported file type
                        dependencies = {};
                        break;
                }
                return dependencies;
            })();
            
            dependencies = await Promise.race([processingPromise, timeoutPromise]);
            
        } finally {
            if (processingTimeout) {
                clearTimeout(processingTimeout);
                processingTimeout = null;
            }
        }
        
        const processingTime = Date.now() - startTime;
        
        return {
            path: file.path,
            type: extension,
            dependencies,
            processingTime,
            size: file.content.length
        };
        
    } catch (error) {
        const processingTime = Date.now() - startTime;
        console.error(`Error processing file ${file.path}:`, error.message);
        
        // Return partial result with error info
        return {
            path: file.path,
            type: getFileExtension(file.path),
            dependencies: {},
            error: {
                message: error.message,
                type: error.name || 'ProcessingError'
            },
            processingTime,
            size: file.content ? file.content.length : 0
        };
    }
}

/**
 * Create empty file result
 */
function createEmptyFileResult(filePath) {
    return {
        path: filePath,
        type: getFileExtension(filePath),
        dependencies: {
            imports: [],
            exports: [],
            functions: [],
            classes: [],
            variables: [],
            events: [],
            workers: []
        },
        empty: true
    };
}

/**
 * Update memory usage tracking
 */
function updateMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
        const current = performance.memory.usedJSHeapSize;
        state.memoryUsage.current = current;
        
        if (current > state.memoryUsage.peak) {
            state.memoryUsage.peak = current;
        }
        
        // Warn if memory usage is high
        const limit = performance.memory.jsHeapSizeLimit;
        const percentage = current / limit;
        
        if (percentage > 0.8) {
            console.warn(`High memory usage in worker: ${Math.round(percentage * 100)}%`);
            
            // Trigger cleanup if available
            if (typeof gc !== 'undefined') {
                try {
                    gc();
                } catch (e) {
                    // Ignore gc errors
                }
            }
        }
    }
}

/**
 * Build the dependency graph from processed files
 * @param {Array} processedFiles - Array of processed files
 * @returns {Object} - Dependency graph with nodes and links
 */
function buildDependencyGraph(processedFiles) {
    console.log('=== BUILDING DEPENDENCY GRAPH ===');
    console.log(`Processing ${processedFiles.length} files`);
    console.log('Sample processed files:', processedFiles.slice(0, 3).map(f => ({
        path: f.path,
        hasDependencies: !!f.dependencies,
        dependencyKeys: f.dependencies ? Object.keys(f.dependencies) : [],
        importsCount: f.dependencies?.imports?.length || 0,
        exportsCount: f.dependencies?.exports?.length || 0,
        functionsCount: f.dependencies?.functions?.length || 0
    })));
    
    const nodes = [];
    const links = [];
    const nodeMap = {};
    const pathToNodeMap = new Map();
    
    // Create nodes with enhanced metadata
    processedFiles.forEach((file, index) => {
        if (!file) return;
        
        const fileName = file.path.split('/').pop();
        const fileExtension = fileName.split('.').pop().toLowerCase();
        
        // Calculate complexity indicators
        const functionsCount = (file.dependencies.functions || []).length;
        const classesCount = (file.dependencies.classes || []).length;
        const variablesCount = (file.dependencies.variables || []).length;
        const importsCount = (file.dependencies.imports || []).length;
        const exportsCount = (file.dependencies.exports || []).length;
        const eventsCount = (file.dependencies.events || []).length;
        
        const complexity = functionsCount + classesCount + variablesCount;
        const connectivity = importsCount + exportsCount;
        
        // Determine node category based on content
        let category = 'file';
        if (fileName.includes('index') || fileName.includes('main') || fileName.includes('app')) {
            category = 'entry';
        } else if (fileName.includes('config') || fileName.includes('settings')) {
            category = 'config';
        } else if (fileName.includes('util') || fileName.includes('helper')) {
            category = 'utility';
        } else if (fileName.includes('component') || fileName.includes('widget')) {
            category = 'component';
        } else if (fileName.includes('service') || fileName.includes('api')) {
            category = 'service';
        } else if (fileName.includes('test') || fileName.includes('spec')) {
            category = 'test';
        }
        
        const node = {
            id: file.path,
            path: file.path,
            name: fileName,
            type: fileExtension,
            category: category,
            size: file.size || 0,
            
            // Complexity indicators
            complexity: complexity,
            connectivity: connectivity,
            functionsCount: functionsCount,
            classesCount: classesCount,
            variablesCount: variablesCount,
            importsCount: importsCount,
            exportsCount: exportsCount,
            eventsCount: eventsCount,
            
            // Risk indicators
            isLarge: (file.size || 0) > 50000, // Files larger than 50KB
            isComplex: complexity > 10,
            isHighlyConnected: connectivity > 5,
            hasEvents: eventsCount > 0,
            
            // Content details
            imports: file.dependencies.imports || [],
            exports: file.dependencies.exports || [],
            functions: file.dependencies.functions || [],
            classes: file.dependencies.classes || [],
            variables: file.dependencies.variables || [],
            events: file.dependencies.events || [],
            workers: file.dependencies.workers || []
        };
        
        nodes.push(node);
        nodeMap[file.path] = node;
        pathToNodeMap.set(file.path, node);
    });
    
    // Resolve import paths to actual files with improved logic
    const resolveImportPath = (importPath, currentFilePath) => {
        // Skip node modules and external dependencies
        if (isNodeModule(importPath)) {
            return null;
        }
        
        // Handle relative imports
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/'));
            let resolvedPath = normalizePath(importPath, currentDir);
            
            // Remove leading './' if present
            if (resolvedPath.startsWith('./')) {
                resolvedPath = resolvedPath.substring(2);
            }
            
            // Try to find exact match first
            if (pathToNodeMap.has(resolvedPath)) {
                return resolvedPath;
            }
            
            // Try with common extensions
            const extensions = ['.js', '.ts', '.jsx', '.tsx', '.json', '.mjs'];
            for (const ext of extensions) {
                const pathWithExt = resolvedPath + ext;
                if (pathToNodeMap.has(pathWithExt)) {
                    return pathWithExt;
                }
            }
            
            // Try index files
            for (const ext of extensions) {
                const indexPath = resolvedPath + '/index' + ext;
                if (pathToNodeMap.has(indexPath)) {
                    return indexPath;
                }
            }
            
            // Try without extension (in case the file was processed without extension)
            const pathWithoutExt = resolvedPath.replace(/\.[^/.]+$/, "");
            if (pathToNodeMap.has(pathWithoutExt)) {
                return pathWithoutExt;
            }
        }
        
        // For absolute imports, try to find matches
        const allPaths = Array.from(pathToNodeMap.keys());
        
        // Try exact filename match
        const filename = importPath.split('/').pop();
        const exactMatches = allPaths.filter(path => {
            const pathFilename = path.split('/').pop();
            return pathFilename === filename || pathFilename === filename + '.js' ||
                   pathFilename === filename + '.ts' || pathFilename === filename + '.jsx' ||
                   pathFilename === filename + '.tsx' || pathFilename === filename + '.json';
        });
        
        if (exactMatches.length > 0) {
            return exactMatches[0];
        }
        
        // Try partial path matches
        const partialMatches = allPaths.filter(path =>
            path.includes(importPath) || path.endsWith('/' + importPath) ||
            path.endsWith('/' + importPath + '.js') || path.endsWith('/' + importPath + '.ts') ||
            path.endsWith('/' + importPath + '.jsx') || path.endsWith('/' + importPath + '.tsx')
        );
        
        return partialMatches.length > 0 ? partialMatches[0] : null;
    };
    
    // Create links with better path resolution and comprehensive dependency tracking
    console.log('=== BUILDING CONNECTIONS ===');
    let totalConnections = 0;
    
    processedFiles.forEach((file, sourceIndex) => {
        if (!file || !file.dependencies) {
            console.log(`Skipping file: ${file?.path || 'unknown'} - no dependencies`);
            return;
        }
        
        const sourceNode = nodeMap[file.path];
        if (!sourceNode) {
            console.log(`No source node found for: ${file.path}`);
            return;
        }
        
        console.log(`Processing connections for: ${file.path}`);
        console.log(`  - Imports: ${file.dependencies.imports?.length || 0}`);
        console.log(`  - Exports: ${file.dependencies.exports?.length || 0}`);
        console.log(`  - Events: ${file.dependencies.events?.length || 0}`);
        
        // Process imports - these create dependency connections
        if (file.dependencies.imports) {
            file.dependencies.imports.forEach(importItem => {
                console.log(`  Processing import: ${importItem.path}`);
                const resolvedPath = resolveImportPath(importItem.path, file.path);
                console.log(`  Resolved to: ${resolvedPath}`);
                const targetNode = resolvedPath ? nodeMap[resolvedPath] : null;
                
                if (targetNode) {
                    // Determine connection type
                    let connectionType = 'import';
                    if (importItem.isDynamic) {
                        connectionType = 'dynamic-import';
                    }
                    
                    links.push({
                        source: sourceNode.id,
                        target: targetNode.id,
                        type: connectionType,
                        names: importItem.names || [],
                        strength: (importItem.names || []).length || 1
                    });
                    
                    // Add to node's imports
                    sourceNode.imports.push({
                        path: resolvedPath,
                        names: importItem.names || [],
                        type: connectionType
                    });
                    
                    // Increase connectivity
                    sourceNode.connectivity++;
                    targetNode.connectivity++;
                    
                    totalConnections++;
                    console.log(`  ✓ Created ${connectionType} connection: ${sourceNode.name} -> ${targetNode.name}`);
                } else {
                    console.log(`  ✗ Could not resolve import path: ${importItem.path} from ${file.path}`);
                    if (resolvedPath) {
                        console.log(`  ✗ Resolved path not found in nodeMap: ${resolvedPath}`);
                        console.log(`  Available paths:`, Object.keys(nodeMap).slice(0, 5));
                    }
                }
            });
        }
        
        // Process workers - these create worker connections
        if (file.dependencies.workers) {
            file.dependencies.workers.forEach(workerItem => {
                console.log(`  Processing worker: ${workerItem.path}`);
                const resolvedPath = resolveImportPath(workerItem.path, file.path);
                console.log(`  Resolved to: ${resolvedPath}`);
                const targetNode = resolvedPath ? nodeMap[resolvedPath] : null;
                
                if (targetNode) {
                    links.push({
                        source: sourceNode.id,
                        target: targetNode.id,
                        type: workerItem.type || 'worker',
                        names: [],
                        strength: 1
                    });
                    
                    // Increase connectivity
                    sourceNode.connectivity++;
                    targetNode.connectivity++;
                    
                    totalConnections++;
                    console.log(`  ✓ Created worker connection: ${sourceNode.name} -> ${targetNode.name}`);
                } else {
                    console.log(`  ✗ Could not resolve worker path: ${workerItem.path} from ${file.path}`);
                }
            });
        }
        
        // Process exports (for re-exports) - these also create connections
        if (file.dependencies.exports) {
            file.dependencies.exports.forEach(exportItem => {
                if (exportItem.path) {
                    const resolvedPath = resolveImportPath(exportItem.path, file.path);
                    const targetNode = resolvedPath ? nodeMap[resolvedPath] : null;
                    
                    if (targetNode) {
                        links.push({
                            source: sourceNode.id,
                            target: targetNode.id,
                            type: 'export',
                            names: exportItem.names || [],
                            strength: (exportItem.names || []).length || 1
                        });
                        
                        console.log(`Created export connection: ${sourceNode.id} -> ${targetNode.id}`);
                    }
                }
                
                // Add to node's exports
                if (exportItem.name) {
                    sourceNode.exports.push({
                        name: exportItem.name,
                        isDefault: exportItem.isDefault || false
                    });
                }
            });
        }
        
        // Process events - these can create event connections
        if (file.dependencies.events) {
            file.dependencies.events.forEach(eventItem => {
                // Look for event listeners that might connect to other files
                const eventName = eventItem.name;
                
                // Find other files that might dispatch this event
                processedFiles.forEach(otherFile => {
                    if (otherFile.path !== file.path && otherFile.dependencies.events) {
                        const hasMatchingEvent = otherFile.dependencies.events.some(otherEvent =>
                            otherEvent.name === eventName && otherEvent.type !== 'listener'
                        );
                        
                        if (hasMatchingEvent) {
                            const otherNode = nodeMap[otherFile.path];
                            if (otherNode) {
                                links.push({
                                    source: otherNode.id,
                                    target: sourceNode.id,
                                    type: 'event',
                                    names: [eventName],
                                    strength: 1
                                });
                                
                                console.log(`Created event connection: ${otherNode.id} -> ${sourceNode.id} (${eventName})`);
                            }
                        }
                    }
                });
            });
        }
    });
    
    // Add cluster information for better visualization
    const clusters = {};
    nodes.forEach(node => {
        const pathParts = node.path.split('/');
        const folder = pathParts.length > 1 ? pathParts[pathParts.length - 2] : 'root';
        
        if (!clusters[folder]) {
            clusters[folder] = [];
        }
        clusters[folder].push(node.id);
        node.cluster = folder;
    });
    
    // Log detailed statistics for debugging
    const connectionStats = {
        total: links.length,
        import: links.filter(l => l.type === 'import').length,
        export: links.filter(l => l.type === 'export').length,
        worker: links.filter(l => l.type === 'worker').length,
        event: links.filter(l => l.type === 'event').length,
        other: links.filter(l => !['import', 'export', 'worker', 'event'].includes(l.type)).length
    };
    
    console.log(`=== FINAL DEPENDENCY GRAPH STATS ===`);
    console.log(`Built dependency graph with ${nodes.length} nodes and ${links.length} links`);
    console.log(`Total connections created during processing: ${totalConnections}`);
    console.log('Connection statistics:', connectionStats);
    console.log('Clusters:', Object.keys(clusters));
    console.log('Sample links:', links.slice(0, 5));
    console.log('Sample nodes with data:', nodes.slice(0, 3).map(n => ({
        name: n.name,
        importsCount: n.importsCount,
        exportsCount: n.exportsCount,
        functionsCount: n.functionsCount,
        actualImports: n.imports?.length || 0,
        actualExports: n.exports?.length || 0
    })));
    console.log(`=== END DEPENDENCY GRAPH STATS ===`);
    
    return { nodes, links, clusters };
}

/**
 * Process all files and build the dependency graph
 */
async function processAllFiles() {
    console.log('Worker: Starting to process', state.files.length, 'files');
    const processedFiles = [];
    
    // Process files in smaller chunks with yielding to prevent blocking
    const chunkSize = 5; // Smaller chunks for better responsiveness
    const totalFiles = state.files.length;
    let lastHeartbeat = Date.now();
    const heartbeatInterval = 5000; // Send heartbeat every 5 seconds
    
    for (let i = 0; i < totalFiles; i += chunkSize) {
        const chunk = state.files.slice(i, Math.min(i + chunkSize, totalFiles));
        
        // Send heartbeat to keep worker alive during long processing
        const now = Date.now();
        if (now - lastHeartbeat > heartbeatInterval) {
            self.postMessage({
                type: 'heartbeat',
                timestamp: now,
                processed: state.processedFiles,
                total: state.totalFiles
            });
            lastHeartbeat = now;
        }
        
        // Process chunk
        for (const file of chunk) {
            try {
                const processed = await processFile(file);
                if (processed) {
                    processedFiles.push(processed);
                }
                
                state.processedFiles++;
                
                // Report progress every 10 files
                if (state.processedFiles % 10 === 0 || state.processedFiles === state.totalFiles) {
                    self.postMessage({
                        type: 'processing_progress',
                        processed: state.processedFiles,
                        total: state.totalFiles,
                        phase: 'parsing',
                        filePath: file.path,
                        memoryUsage: state.memoryUsage
                    });
                }
            } catch (error) {
                console.error('Worker: Error processing file', file.path, error);
                self.postMessage({
                    type: 'error',
                    message: `Error processing file ${file.path}: ${error.message}`
                });
                state.processedFiles++; // Count failed files too
            }
        }
        
        // Yield control after each chunk to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Additional yield every 50 files for larger datasets
        if (i % 50 === 0 && i > 0) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }
    }
    
    console.log('Worker: Processed', processedFiles.length, 'files successfully');
    
    // Build dependency graph
    self.postMessage({
        type: 'processing_progress',
        processed: state.processedFiles,
        total: state.totalFiles,
        phase: 'building graph'
    });
    
    const graph = buildDependencyGraph(processedFiles);
    
    // Create the result structure expected by main.js
    const result = {
        files: {},
        fileContents: {},
        graph: {
            nodes: graph.nodes,
            edges: graph.links  // Convert 'links' to 'edges'
        },
        codeMap: {},
        metadata: {
            totalFiles: state.totalFiles,
            processedFiles: state.processedFiles,
            timestamp: Date.now()
        }
    };
    
    // Populate files data
    state.files.forEach((file, index) => {
        if (file && file.path) {
            const node = graph.nodes.find(n => n.path === file.path);
            if (node) {
                result.files[file.path] = {
                    functions: node.functions || [],
                    variables: node.variables || [],
                    events: node.events || [],
                    imports: node.imports || [],
                    exports: node.exports || [],
                    workers: node.workers || []
                };
            }
            
            // Store file content for code preview
            result.fileContents[file.path] = file.content;
        }
    });
    
    console.log('Worker sending result with', result.graph.nodes.length, 'nodes and', result.graph.edges.length, 'edges');
    
    // Send results back to main thread
    self.postMessage({
        type: 'workerResults',
        data: result
    });
}

// Handle messages from main thread
self.onmessage = function(event) {
    const data = event.data;
    const messageId = data.messageId;
    
    try {
        switch (data.type) {
            case 'init':
                console.log('Worker: Received init message');
                console.log(`Worker: Current state.files.length before init: ${state.files.length}`);

                state.projectRootPath = data.projectRootPath || '';
                state.totalFiles = data.totalFilesToExpect || 0;

                // Ensure excludeRules is always an array
                if (Array.isArray(data.excludeRules)) {
                    state.excludeRules = data.excludeRules;
                } else if (data.excludeRules && Array.isArray(data.excludeRules.folders)) {
                    state.excludeRules = data.excludeRules.folders;
                } else {
                    state.excludeRules = [];
                }

                // Don't clear files array here - files might already be loaded
                // Only initialize if not already initialized
                if (!state.files) {
                    state.files = [];
                    console.log('Worker: Initialized empty files array');
                } else {
                    console.log(`Worker: Preserving existing ${state.files.length} files in array`);
                }
                state.processedFiles = 0;

                console.log(`Worker: After init - state.files.length: ${state.files.length}, totalFiles: ${state.totalFiles}`);
                
                // Send acknowledgment with messageId
                self.postMessage({
                    type: 'init_complete',
                    messageId: messageId,
                    success: true,
                    message: `Worker initialized with ${state.totalFiles} files to process and ${state.excludeRules.length} exclude rules`
                });
                break;
                
            case 'fileChunk':
                console.log('Worker: Received fileChunk with', data.files?.length || 0, 'files');
                console.log('Worker: Sample files in chunk:', data.files?.slice(0, 3).map(f => ({ path: f.path, hasContent: !!f.content })));
                console.log(`Worker: Current state.files.length before adding chunk: ${state.files.length}`);

                if (Array.isArray(data.files)) {
                    const beforeCount = state.files.length;

                    // Ensure state.files is initialized
                    if (!state.files) {
                        console.log('Worker: state.files was null/undefined, initializing...');
                        state.files = [];
                    }

                    state.files.push(...data.files);
                    const afterCount = state.files.length;

                    console.log(`Worker: Added ${data.files.length} files to state. Before: ${beforeCount}, After: ${afterCount}`);
                    console.log('Worker: Sample files in state:', state.files.slice(0, 3).map(f => ({ path: f.path, hasContent: !!f.content })));
                    console.log(`Worker: Total files in state now: ${state.files.length}`);

                    // Send acknowledgment with messageId
                    self.postMessage({
                        type: 'fileChunk_complete',
                        messageId: messageId,
                        success: true,
                        message: `Received ${data.files.length} files, total now ${state.files.length}`
                    });
                } else {
                    self.postMessage({
                        type: 'error',
                        messageId: messageId,
                        success: false,
                        message: 'Invalid files data received'
                    });
                }
                break;
                
            case 'processAll':
                console.log(`Worker: ProcessAll called. Current state.files.length: ${state.files.length}`);
                console.log(`Worker: state.totalFiles: ${state.totalFiles}`);
                console.log('Worker: Sample files in state before processing:', state.files.slice(0, 3).map(f => ({ path: f.path, hasContent: !!f.content, contentLength: f.content?.length || 0 })));

                // Send immediate acknowledgment
                self.postMessage({
                    type: 'processAll_started',
                    messageId: messageId,
                    success: true,
                    message: 'Processing started'
                });

                // Start processing asynchronously
                processAllFiles().then(() => {
                    // Processing completion is handled in processAllFiles
                }).catch(error => {
                    self.postMessage({
                        type: 'error',
                        messageId: messageId,
                        success: false,
                        message: `Processing failed: ${error.message}`,
                        stack: error.stack
                    });
                });
                break;
                
            case 'cleanup':
                // Clean up worker state and memory
                cleanupWorker();
                
                // Send acknowledgment with messageId
                self.postMessage({
                    type: 'cleanup_complete',
                    messageId: messageId,
                    success: true,
                    message: 'Worker cleanup completed'
                });
                break;
                
            default:
                self.postMessage({
                    type: 'error',
                    messageId: messageId,
                    success: false,
                    message: `Unknown message type: ${data.type}`
                });
        }
    } catch (error) {
        self.postMessage({
            type: 'error',
            messageId: messageId,
            success: false,
            message: error.message,
            stack: error.stack
        });
    }
};

/**
 * Clean up worker state and free memory
 */
function cleanupWorker() {
    try {
        // Clear all state arrays and objects
        state.files = [];
        state.results.nodes = [];
        state.results.links = [];
        state.results.metadata = {};
        state.excludeRules = [];
        
        // Reset counters
        state.processedFiles = 0;
        state.totalFiles = 0;
        state.projectRootPath = '';
        
        // Clear any cached regex patterns
        Object.keys(patterns).forEach(key => {
            if (patterns[key] && typeof patterns[key].lastIndex !== 'undefined') {
                patterns[key].lastIndex = 0;
            }
        });
        
        self.postMessage({
            type: 'debug',
            message: 'Worker cleanup completed'
        });
    } catch (error) {
        self.postMessage({
            type: 'error',
            message: `Worker cleanup error: ${error.message}`
        });
    }
}
