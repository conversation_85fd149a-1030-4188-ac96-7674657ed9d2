/**
 * File Content Cache
 * Manages on-demand loading of file content for code preview
 */

class FileContentCache {
    constructor(options = {}) {
        this.cache = new Map();
        this.maxCacheSize = options.maxCacheSize || 20; // Cache up to 20 files
        this.maxFileSize = options.maxFileSize || 5 * 1024 * 1024; // 5MB max
        this.accessOrder = []; // For LRU eviction
        this.originalFiles = null; // Reference to original file list
    }

    /**
     * Set the original files for re-reading
     */
    setOriginalFiles(files) {
        this.originalFiles = files;
    }

    /**
     * Get file content with on-demand loading
     */
    async getFileContent(filePath) {
        // Check if already in cache
        if (this.cache.has(filePath)) {
            this.updateAccessOrder(filePath);
            return this.cache.get(filePath);
        }

        // Check if content is available in scan results
        if (window.scanResults?.fileContents?.[filePath]) {
            const content = window.scanResults.fileContents[filePath];
            
            // If it's a truncated object, try to reload from original files
            if (typeof content === 'object' && content.truncated) {
                const reloadedContent = await this.reloadFileFromOriginal(filePath);
                if (reloadedContent) {
                    this.addToCache(filePath, reloadedContent);
                    return reloadedContent;
                }
                // Fall back to preview if reload fails
                return content.preview + '\n\n' + content.message;
            }
            
            // If it's a string, cache and return it
            if (typeof content === 'string') {
                this.addToCache(filePath, content);
                return content;
            }
        }

        // Try to reload from original files
        const reloadedContent = await this.reloadFileFromOriginal(filePath);
        if (reloadedContent) {
            this.addToCache(filePath, reloadedContent);
            return reloadedContent;
        }

        return null;
    }

    /**
     * Reload file content from original file list
     */
    async reloadFileFromOriginal(filePath) {
        if (!this.originalFiles) {
            console.warn('FileContentCache: No original files available for reload');
            return null;
        }

        // Find the file in the original file list
        const file = Array.from(this.originalFiles).find(f => 
            (f.webkitRelativePath || f.name) === filePath
        );

        if (!file) {
            console.warn(`FileContentCache: File not found in original files: ${filePath}`);
            return null;
        }

        // Check file size
        if (file.size > this.maxFileSize) {
            console.warn(`FileContentCache: File too large to reload: ${filePath} (${file.size} bytes)`);
            return null;
        }

        try {
            const content = await this.readFile(file);
            console.log(`FileContentCache: Reloaded file content for ${filePath}`);
            return content;
        } catch (error) {
            console.error(`FileContentCache: Failed to reload file ${filePath}:`, error);
            return null;
        }
    }

    /**
     * Read file content
     */
    async readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            
            // Set timeout for file reading
            setTimeout(() => {
                if (reader.readyState === FileReader.LOADING) {
                    reader.abort();
                    reject(new Error('File reading timeout'));
                }
            }, 10000); // 10 second timeout
            
            reader.readAsText(file);
        });
    }

    /**
     * Add content to cache with LRU eviction
     */
    addToCache(filePath, content) {
        // Remove if already exists
        if (this.cache.has(filePath)) {
            this.cache.delete(filePath);
            this.accessOrder = this.accessOrder.filter(path => path !== filePath);
        }

        // Evict oldest if cache is full
        if (this.cache.size >= this.maxCacheSize) {
            const oldestPath = this.accessOrder.shift();
            if (oldestPath) {
                this.cache.delete(oldestPath);
            }
        }

        // Add to cache
        this.cache.set(filePath, content);
        this.accessOrder.push(filePath);
    }

    /**
     * Update access order for LRU
     */
    updateAccessOrder(filePath) {
        this.accessOrder = this.accessOrder.filter(path => path !== filePath);
        this.accessOrder.push(filePath);
    }

    /**
     * Get cache statistics
     */
    getStats() {
        const totalSize = Array.from(this.cache.values())
            .reduce((sum, content) => sum + (typeof content === 'string' ? content.length : 0), 0);

        return {
            cachedFiles: this.cache.size,
            maxCacheSize: this.maxCacheSize,
            totalSize,
            hasOriginalFiles: !!this.originalFiles,
            cacheUtilization: Math.round((this.cache.size / this.maxCacheSize) * 100)
        };
    }

    /**
     * Clear cache
     */
    clear() {
        this.cache.clear();
        this.accessOrder = [];
    }

    /**
     * Check if file content is available (cached or can be loaded)
     */
    async isContentAvailable(filePath) {
        if (this.cache.has(filePath)) {
            return true;
        }

        if (window.scanResults?.fileContents?.[filePath]) {
            return true;
        }

        if (this.originalFiles) {
            const file = Array.from(this.originalFiles).find(f => 
                (f.webkitRelativePath || f.name) === filePath
            );
            return !!file && file.size <= this.maxFileSize;
        }

        return false;
    }

    /**
     * Preload content for specific files
     */
    async preloadFiles(filePaths) {
        const promises = filePaths.map(async (filePath) => {
            try {
                await this.getFileContent(filePath);
            } catch (error) {
                console.warn(`FileContentCache: Failed to preload ${filePath}:`, error);
            }
        });

        await Promise.all(promises);
    }

    /**
     * Get content snippet around a specific line
     */
    async getContentSnippet(filePath, lineNumber, contextLines = 10) {
        const content = await this.getFileContent(filePath);
        if (!content || typeof content !== 'string') {
            return null;
        }

        const lines = content.split('\n');
        const targetLineIndex = lineNumber - 1; // Convert to 0-based index
        
        const startLine = Math.max(0, targetLineIndex - contextLines);
        const endLine = Math.min(lines.length - 1, targetLineIndex + contextLines);
        
        const snippet = lines.slice(startLine, endLine + 1).join('\n');
        const snippetStartLine = startLine + 1; // Convert back to 1-based
        
        return {
            content: snippet,
            startLine: snippetStartLine,
            targetLine: lineNumber,
            totalLines: lines.length,
            isComplete: startLine === 0 && endLine === lines.length - 1
        };
    }
}

// Global instance
let fileContentCache = null;

/**
 * Get or create the global file content cache
 */
export function getFileContentCache() {
    if (!fileContentCache) {
        fileContentCache = new FileContentCache({
            maxCacheSize: 20,
            maxFileSize: 5 * 1024 * 1024 // 5MB
        });
    }
    return fileContentCache;
}

/**
 * Initialize the cache with original files
 */
export function initializeFileContentCache(originalFiles) {
    const cache = getFileContentCache();
    cache.setOriginalFiles(originalFiles);
    return cache;
}

export default FileContentCache;
